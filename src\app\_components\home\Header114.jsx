"use client";

import { But<PERSON> } from "@relume_io/relume-ui";
import React, { useEffect, useRef, useState } from "react";

export function Header114() {
  const sectionRef = useRef(null);
  const [isFixed, setIsFixed] = useState(true);
  const [backgroundTop, setBackgroundTop] = useState(0);
  const [parallaxOffset, setParallaxOffset] = useState(0);
  const [finalParallaxOffset, setFinalParallaxOffset] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      if (sectionRef.current) {
        const sectionRect = sectionRef.current.getBoundingClientRect();
        const sectionBottom = sectionRect.bottom;
        const viewportHeight = window.innerHeight;
        const scrollY = window.scrollY;

        // Calculate parallax offset (subtle movement - 20% of scroll speed)
        const sectionTop = sectionRect.top + scrollY;
        const scrollProgress = Math.max(0, scrollY - sectionTop);
        const parallaxSpeed = 0.2; // Adjust this value for more/less parallax effect
        const currentParallaxOffset = scrollProgress * parallaxSpeed;
        setParallaxOffset(currentParallaxOffset);

        // When the section bottom reaches the bottom of the viewport, disable fixed background
        if (sectionBottom <= viewportHeight) {
          if (isFixed) {
            // Store the final parallax offset when transitioning from fixed to absolute
            setFinalParallaxOffset(currentParallaxOffset);
          }
          setIsFixed(false);
          // Calculate where the background should be positioned to maintain continuity
          const sectionHeight = sectionRef.current.offsetHeight;
          setBackgroundTop(sectionTop + sectionHeight - viewportHeight);
        } else {
          setIsFixed(true);
          setBackgroundTop(0);
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isFixed]);

  return (
    <section ref={sectionRef} id="relume" className="relative min-h-[200vh]">
      {/* Sticky background image that stays fixed while content scrolls over it */}
      <div
        className={`${isFixed ? 'fixed' : 'absolute'} h-screen w-full overflow-hidden -z-50`}
        style={{
          top: isFixed ? 0 : `${backgroundTop}px`,
          backgroundImage: "url('/images/forestforward/homepage/9.png')",
          backgroundAttachment: "fixed",
          backgroundSize: "110%", // Slightly larger to allow for parallax movement
          backgroundPosition: `center ${isFixed ? -parallaxOffset : -finalParallaxOffset}px`,
          backgroundRepeat: "no-repeat",
          transition: 'none' // Remove transition to prevent glitches
        }}
      >
        <div className="absolute inset-0 bg-black/75" />
      </div>

      {/* Content that scrolls over the sticky background */}
      <div className="absolute inset-0 z-50 px-[5%]">
        <div className="container min-h-[200vh] flex flex-col justify-between">
          {/* First content section */}
          <div className="flex flex-col justify-center min-h-screen py-20">
            <div className="w-full max-w-4xl pl-[5%] pr-[20%]">
              <h1 className="text-6xl font-semibold text-text-alternative md:text-9xl lg:text-10xl mb-6 md:mb-8">
                Walk the talk, zeggen ze.<br></br> Wij doen het.
              </h1>
              <div className="mt-6 flex flex-wrap gap-4 md:mt-8">
                <Button
                  title="Button"
                  className="group relative overflow-hidden border-none bg-background-primary text-text-primary rounded-lg px-6 py-3 font-medium transition-all duration-300 ease-in-out hover:bg-background-secondary hover:shadow-lg active:scale-95 active:bg-background-tertiary focus:outline-none focus:ring-2 focus:ring-background-secondary focus:ring-opacity-50"
                >
                  <span className="relative z-10 transition-colors duration-300">
                    Ontdek meer
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-background-secondary to-background-tertiary transform scale-x-0 origin-left transition-transform duration-300 ease-out group-hover:scale-x-100"></div>
                </Button>
                <Button
                  title="Button"
                  variant="secondary"
                  className="group relative overflow-hidden border border-border-50 bg-transparent text-text-alternative rounded-lg px-6 py-3 font-medium transition-all duration-300 ease-in-out hover:border-text-alternative hover:bg-text-alternative hover:text-text-primary active:scale-95 active:bg-background-secondary active:text-text-primary focus:outline-none focus:ring-2 focus:ring-text-alternative focus:ring-opacity-50"
                >
                  <span className="relative z-10 transition-colors duration-300">
                    Contacteer ons
                  </span>
                  <div className="absolute inset-0 bg-text-alternative transform scale-x-0 origin-left transition-transform duration-300 ease-out group-hover:scale-x-100"></div>
                </Button>
              </div>
            </div>
          </div>

          {/* Second content section */}
          <div className="flex flex-col justify-center min-h-screen py-20">
            <div className="w-full max-w-2xl pl-[40%] pr-[5%]">
              <h1 className="text-4xl font-semibold text-text-alternative md:text-5xl lg:text-6xl mb-6">
                Samen maken we<br></br> duurzaamheid tastbaar.
              </h1>
              <p className="text-text-alternative md:text-md ">
                Klinkt mooi, maar we dóen het ook. Samen met jullie, bedrijven
                groot en klein. En dan hebben we het over duurzaamheid in al z'n
                vormen. Over impact maken. Op alle mogelijke manieren. Dat is de
                meerwaarde van ons ecosysteem. We rollen de mouwen op om samen
                lokaal natuur te creëren en jouw ESG-verhaal tastbaar te maken.
                Om jouw bedrijf een stem te geven, zodat je anderen kan
                inspireren. En om de betrokkenheid van jouw stakeholders te
                vergroten door ze slim én leuk te verbinden. Zorgen voor
                daadkracht. En draagvlak creëren. Inspireren en verbinden. Dat
                is wat we doen!
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}